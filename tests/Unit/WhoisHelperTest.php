<?php

use App\Helpers\WhoisHelper;

describe('WhoisHelper', function () {
    describe('isValidDomain', function () {
        it('validates standard domains correctly', function () {
            expect(WhoisHelper::isValidDomain('example.com'))->toBeTrue();
            expect(WhoisHelper::isValidDomain('google.com'))->toBeTrue();
            expect(WhoisHelper::isValidDomain('test.org'))->toBeTrue();
            expect(WhoisHelper::isValidDomain('site.net'))->toBeTrue();
            expect(WhoisHelper::isValidDomain('domain.info'))->toBeTrue();
        });

        it('validates domains with subdomains', function () {
            expect(WhoisHelper::isValidDomain('www.example.com'))->toBeTrue();
            expect(WhoisHelper::isValidDomain('mail.google.com'))->toBeTrue();
            expect(WhoisHelper::isValidDomain('api.test.org'))->toBeTrue();
            expect(WhoisHelper::isValidDomain('sub.domain.example.com'))->toBeTrue();
        });

        it('validates domains with numbers and hyphens', function () {
            expect(WhoisHelper::isValidDomain('test-site.com'))->toBeTrue();
            expect(WhoisHelper::isValidDomain('123domain.com'))->toBeTrue();
            expect(WhoisHelper::isValidDomain('site123.org'))->toBeTrue();
            expect(WhoisHelper::isValidDomain('test-123.example.com'))->toBeTrue();
        });

        it('validates internationalized domain names (IDN)', function () {
            expect(WhoisHelper::isValidDomain('unilån.dk'))->toBeTrue();
            expect(WhoisHelper::isValidDomain('münchen.de'))->toBeTrue();
            expect(WhoisHelper::isValidDomain('тест.рф'))->toBeTrue();
            expect(WhoisHelper::isValidDomain('例え.テスト'))->toBeTrue();
        });

        it('handles case insensitivity correctly', function () {
            expect(WhoisHelper::isValidDomain('EXAMPLE.COM'))->toBeTrue();
            expect(WhoisHelper::isValidDomain('Example.Com'))->toBeTrue();
            expect(WhoisHelper::isValidDomain('TEST.ORG'))->toBeTrue();
        });

        it('handles whitespace correctly', function () {
            expect(WhoisHelper::isValidDomain(' example.com '))->toBeTrue();
            expect(WhoisHelper::isValidDomain('  test.org  '))->toBeTrue();
            expect(WhoisHelper::isValidDomain("\texample.com\n"))->toBeTrue();
        });

        it('rejects empty or whitespace-only domains', function () {
            expect(WhoisHelper::isValidDomain(''))->toBeFalse();
            expect(WhoisHelper::isValidDomain(' '))->toBeFalse();
            expect(WhoisHelper::isValidDomain('   '))->toBeFalse();
            expect(WhoisHelper::isValidDomain("\t"))->toBeFalse();
            expect(WhoisHelper::isValidDomain("\n"))->toBeFalse();
        });

        it('rejects domains that are too long', function () {
            // Create a domain longer than 255 characters
            $longDomain = str_repeat('a', 250) . '.com';
            expect(WhoisHelper::isValidDomain($longDomain))->toBeFalse();
            
            // Test exactly 255 characters (should be rejected)
            $exactlyLongDomain = str_repeat('a', 251) . '.com';
            expect(WhoisHelper::isValidDomain($exactlyLongDomain))->toBeFalse();
        });

        it('rejects domains without TLD', function () {
            expect(WhoisHelper::isValidDomain('example'))->toBeFalse();
            expect(WhoisHelper::isValidDomain('test'))->toBeFalse();
            expect(WhoisHelper::isValidDomain('domain'))->toBeFalse();
        });

        it('rejects domains with invalid characters', function () {
            expect(WhoisHelper::isValidDomain('example..com'))->toBeFalse();
            expect(WhoisHelper::isValidDomain('.example.com'))->toBeFalse();
            expect(WhoisHelper::isValidDomain('example.com.'))->toBeFalse();
            expect(WhoisHelper::isValidDomain('exam_ple.com'))->toBeFalse();
            expect(WhoisHelper::isValidDomain('example@.com'))->toBeFalse();
            expect(WhoisHelper::isValidDomain('example#.com'))->toBeFalse();
        });

        it('rejects domains with invalid hyphens', function () {
            expect(WhoisHelper::isValidDomain('-example.com'))->toBeFalse();
            expect(WhoisHelper::isValidDomain('example-.com'))->toBeFalse();
            // Note: The current regex allows consecutive hyphens within labels
            expect(WhoisHelper::isValidDomain('exam--ple.com'))->toBeTrue();
        });

        it('rejects malformed domains', function () {
            expect(WhoisHelper::isValidDomain('example.'))->toBeFalse();
            expect(WhoisHelper::isValidDomain('.example'))->toBeFalse();
            expect(WhoisHelper::isValidDomain('example..com'))->toBeFalse();
            expect(WhoisHelper::isValidDomain('example.c'))->toBeFalse(); // TLD too short
        });

        it('rejects domains with spaces in the middle', function () {
            expect(WhoisHelper::isValidDomain('exam ple.com'))->toBeFalse();
            expect(WhoisHelper::isValidDomain('example. com'))->toBeFalse();
            expect(WhoisHelper::isValidDomain('example .com'))->toBeFalse();
        });

        it('rejects domains with special characters', function () {
            expect(WhoisHelper::isValidDomain('example!.com'))->toBeFalse();
            expect(WhoisHelper::isValidDomain('example$.com'))->toBeFalse();
            expect(WhoisHelper::isValidDomain('example%.com'))->toBeFalse();
            expect(WhoisHelper::isValidDomain('example&.com'))->toBeFalse();
            expect(WhoisHelper::isValidDomain('example*.com'))->toBeFalse();
        });

        it('validates common TLDs', function () {
            expect(WhoisHelper::isValidDomain('example.co.uk'))->toBeTrue();
            expect(WhoisHelper::isValidDomain('test.com.au'))->toBeTrue();
            expect(WhoisHelper::isValidDomain('site.gov.uk'))->toBeTrue();
            expect(WhoisHelper::isValidDomain('domain.ac.uk'))->toBeTrue();
        });

        it('validates new gTLDs', function () {
            expect(WhoisHelper::isValidDomain('example.tech'))->toBeTrue();
            expect(WhoisHelper::isValidDomain('test.blog'))->toBeTrue();
            expect(WhoisHelper::isValidDomain('site.app'))->toBeTrue();
            expect(WhoisHelper::isValidDomain('domain.dev'))->toBeTrue();
        });

        it('handles edge cases with IDN conversion', function () {
            // Test cases where IDN conversion might fail but domain should still be validated
            // Note: Mixed punycode/unicode domains may not validate correctly
            expect(WhoisHelper::isValidDomain('xn--unilån-gra.dk'))->toBeFalse(); // Mixed punycode/unicode
            expect(WhoisHelper::isValidDomain('xn--mnchen-3ya.de'))->toBeTrue(); // Pure punycode
            expect(WhoisHelper::isValidDomain('xn--uniln-gra.dk'))->toBeTrue(); // Correct punycode for unilån.dk
        });

        it('validates very long but valid domains', function () {
            // Create a domain that's long but still under 255 characters
            $validLongDomain = str_repeat('a', 60) . '.' . str_repeat('b', 60) . '.com';
            expect(WhoisHelper::isValidDomain($validLongDomain))->toBeTrue();
        });

        it('validates numeric TLDs', function () {
            // Note: The current regex allows numeric TLDs
            expect(WhoisHelper::isValidDomain('example.123'))->toBeTrue();
            expect(WhoisHelper::isValidDomain('test.456'))->toBeTrue();
            // But single character TLDs are rejected
            expect(WhoisHelper::isValidDomain('example.1'))->toBeFalse();
        });
    });

    describe('categorizeError', function () {
        it('categorizes database errors correctly', function () {
            $databaseError = new Exception('SQLSTATE[22001]: String data, right truncated: 1406 Data too long for column \'domain_owner\' at row 1');
            $reflection = new ReflectionClass(WhoisHelper::class);
            $method = $reflection->getMethod('categorizeError');
            $method->setAccessible(true);

            expect($method->invoke(null, $databaseError))->toBe('database_error');
        });

        it('categorizes network errors correctly', function () {
            $networkError = new Exception('Connection timed out');
            $reflection = new ReflectionClass(WhoisHelper::class);
            $method = $reflection->getMethod('categorizeError');
            $method->setAccessible(true);

            expect($method->invoke(null, $networkError))->toBe('network_error');
        });

        it('categorizes whois errors correctly', function () {
            $whoisError = new Exception('No whois server found for domain');
            $reflection = new ReflectionClass(WhoisHelper::class);
            $method = $reflection->getMethod('categorizeError');
            $method->setAccessible(true);

            expect($method->invoke(null, $whoisError))->toBe('whois_error');
        });

        it('categorizes unknown errors correctly', function () {
            $unknownError = new Exception('Some random error message');
            $reflection = new ReflectionClass(WhoisHelper::class);
            $method = $reflection->getMethod('categorizeError');
            $method->setAccessible(true);

            expect($method->invoke(null, $unknownError))->toBe('unknown_error');
        });
    });
});
