APP_NAME=Laedfy
APP_ENV=local
APP_KEY=base64:FMOeRv+MrCm9gl04Y6tp7Eaks98LKR23zfAo7QUz0U8=
APP_DEBUG=true
APP_TIMEZONE=UTC
APP_URL=http://localhost

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=bugsnag
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=sqlite
# DB_HOST=127.0.0.1
# DB_PORT=3306
# DB_DATABASE=laravel
# DB_USERNAME=root
# DB_PASSWORD=

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database

CACHE_STORE=database
CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"


# Bugsnag
BUGSNAG_API_KEY=e52df1b0b2847bde28942479b67f2f1e
BUGSNAG_LOGGER_LEVEL=error

# Google
CRAWLER_GOOGLE_API=

# Bright Data
CRAWLER_BRIGHTDATA_API_KEY=
CRAWLER_BRIGHTDATA_CUSTOMER_ID=
CRAWLER_BRIGHTDATA_ZONE_ID=

# Brave Search
CRAWLER_BRAVE_SEARCH_API=

# Serp APIs
CRAWLER_SERPAPI_API=
CRAWLER_SERPSTACK_API=

# Affiliate Partner APIs
CRAWLER_PARTNER_ADS_API=
CRAWLER_ADTRACTION_API=
CRAWLER_AWIN_API=
CRAWLER_KELKOO_API=

# Domain Availability Service API
CURANET_DAS_API_BASE_URL=https://api.curanet.dk/das
CURANET_DAS_API_CLIENT_ID=
CURANET_DAS_API_SECRET=
