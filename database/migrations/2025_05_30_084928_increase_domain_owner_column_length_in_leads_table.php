<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('leads', function (Blueprint $table) {
            // Increase domain_owner column length from 50 to 255 characters
            // to accommodate longer organization names from WHOIS data
            $table->string('domain_owner', 255)->nullable()->default('')->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('leads', function (Blueprint $table) {
            // Revert domain_owner column length back to 50 characters
            // WARNING: This may cause data loss if any existing records have domain_owner longer than 50 characters
            $table->string('domain_owner', 50)->nullable()->default('')->change();
        });
    }
};
