# WhoisHelper Testing Documentation

## Overview

This document describes the test suite for the `WhoisHelper::isValidDomain` method, which validates domain names according to the implementation's specific regex pattern.

## Test File Location

- **File**: `tests/Unit/WhoisHelperTest.php`
- **Framework**: PestPHP
- **Test Count**: 19 test cases with 69 assertions

## Test Categories

### 1. Valid Domain Tests

#### Standard Domains
- Tests basic domain formats like `example.com`, `google.com`, `test.org`
- Validates common TLDs (`.com`, `.org`, `.net`, `.info`)

#### Domains with Subdomains
- Tests multi-level domains like `www.example.com`, `api.test.org`
- Validates complex subdomain structures

#### Domains with Numbers and Hyphens
- Tests domains containing numbers: `123domain.com`, `site123.org`
- Tests domains with hyphens: `test-site.com`, `test-123.example.com`
- **Note**: The current implementation allows consecutive hyphens within labels

#### Internationalized Domain Names (IDN)
- Tests Unicode domains: `unilån.dk`, `münchen.de`, `тест.рф`
- Tests mixed character sets and punycode conversion

### 2. Input Handling Tests

#### Case Insensitivity
- Validates that domains are accepted regardless of case
- Tests: `EXAMPLE.COM`, `Example.Com`

#### Whitespace Handling
- Tests domains with leading/trailing whitespace
- Validates proper trimming behavior

### 3. Invalid Domain Tests

#### Empty/Whitespace Domains
- Rejects empty strings, spaces, tabs, newlines

#### Length Validation
- Rejects domains longer than 255 characters
- Tests edge cases around the length limit

#### Missing TLD
- Rejects single-word domains without dots
- Tests: `example`, `test`, `domain`

#### Invalid Characters
- Rejects domains with special characters: `@`, `#`, `_`
- Rejects domains with double dots: `example..com`
- Rejects domains starting/ending with dots

#### Malformed Domains
- Tests various malformed patterns
- Validates proper domain structure requirements

### 4. Edge Cases

#### IDN Conversion Edge Cases
- Tests mixed punycode/unicode domains
- Validates behavior when IDN conversion fails

#### Numeric TLDs
- **Note**: The current implementation accepts numeric TLDs like `.123`, `.456`
- This differs from standard domain validation but matches the regex pattern

#### Very Long Valid Domains
- Tests domains that are long but still under the 255-character limit

## Implementation Notes

The tests are designed to match the actual behavior of the `WhoisHelper::isValidDomain` method, which uses this regex pattern:

```php
$pattern = '/^(?:[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?\.)+[a-z0-9][a-z0-9-]{0,61}[a-z0-9]$/i';
```

### Key Behaviors:

1. **Consecutive Hyphens**: The regex allows consecutive hyphens within domain labels
2. **Numeric TLDs**: The regex accepts purely numeric TLDs
3. **IDN Handling**: The method converts IDN domains to ASCII before validation
4. **Case Insensitive**: All domains are converted to lowercase before validation

## Running the Tests

```bash
# Run only WhoisHelper tests
php artisan test tests/Unit/WhoisHelperTest.php

# Run all tests
php artisan test

# Run with verbose output
php artisan test tests/Unit/WhoisHelperTest.php --verbose
```

## Test Structure

The tests use PestPHP's descriptive syntax with nested `describe` blocks:

```php
describe('WhoisHelper', function () {
    describe('isValidDomain', function () {
        it('validates standard domains correctly', function () {
            expect(WhoisHelper::isValidDomain('example.com'))->toBeTrue();
        });
    });
});
```

## Maintenance

When updating the `WhoisHelper::isValidDomain` method:

1. Review the test cases to ensure they still match the expected behavior
2. Add new test cases for any new validation rules
3. Update documentation if the validation logic changes
4. Consider backward compatibility when modifying domain validation rules

## Error Handling Tests

### Error Categorization Tests

The test suite includes tests for the `categorizeError` method that classifies different types of exceptions:

#### Database Errors
- Tests detection of SQL errors like "Data too long for column"
- Validates proper categorization of database constraint violations

#### Network Errors
- Tests detection of connection timeouts
- Validates handling of network connectivity issues

#### WHOIS Errors
- Tests detection of WHOIS-specific errors
- Validates handling of missing WHOIS servers

#### Unknown Errors
- Tests fallback categorization for unrecognized errors

### Error Handling Strategy

The improved error handling follows these principles:

1. **Database Errors**: Don't retry, attempt data truncation and save
2. **Network Errors**: Allow job retries for temporary connectivity issues
3. **WHOIS Errors**: Allow job retries for temporary service issues
4. **Unknown Errors**: Allow job retries with caution

## Dependencies

- **PestPHP**: Testing framework
- **Laravel Testing**: Base test case functionality
- **WhoisHelper**: The class being tested
- **ReflectionClass**: For testing private methods

The tests require no external services or database connections, making them fast and reliable for continuous integration.
