# WhoisHelper Error Handling Documentation

## Overview

This document describes the improved error handling implemented in the `WhoisHelper` class to address common issues encountered during WHOIS lookups, particularly database column length constraints and network timeouts.

## Issues Addressed

### 1. Database Column Length Errors

**Problem**: The `domain_owner` column was limited to 50 characters, causing SQL errors when WHOIS data contained longer organization names.

**Example Error**:
```
SQLSTATE[22001]: String data, right truncated: 1406 Data too long for column 'domain_owner' at row 1
```

**Solution**: 
- Increased `domain_owner` column length from 50 to 255 characters via migration
- Added intelligent data truncation as fallback for extremely long names
- Improved error logging with structured context

### 2. Network Timeout Errors

**Problem**: WHOIS lookups were failing due to network timeouts, especially for certain TLDs or geographic regions.

**Example Error**:
```
Connection timed out
```

**Solution**:
- Categorized network errors separately from database errors
- Allow job retries for network issues while preventing retries for database issues
- Enhanced logging to distinguish between error types

## Implementation Details

### Error Categorization

The `categorizeError()` method classifies exceptions into four categories:

#### Database Errors
- **Triggers**: SQLSTATE errors, "Data too long for column", "String data, right truncated"
- **Handling**: Attempt data truncation, no job retries
- **Rationale**: Database schema issues won't resolve with retries

#### Network Errors  
- **Triggers**: "Connection timed out", "timeout", "Connection refused", "Network is unreachable"
- **Handling**: Mark domain as unavailable, allow job retries
- **Rationale**: Network issues are often temporary

#### WHOIS Errors
- **Triggers**: "WHOIS", "No whois server"
- **Handling**: Mark domain as unavailable, allow job retries  
- **Rationale**: WHOIS service issues are often temporary

#### Unknown Errors
- **Triggers**: Any other exception
- **Handling**: Mark domain as unavailable, allow job retries
- **Rationale**: Conservative approach for unrecognized errors

### Database Error Handling

When a database error is detected:

1. **Check Error Type**: Specifically look for `domain_owner` column length issues
2. **Truncate Data**: Reduce `domain_owner` to 250 characters + "..." ellipsis
3. **Retry Save**: Attempt to save the lead with truncated data
4. **Fallback**: If truncation fails, save with error message and status 0
5. **Log Warning**: Record the truncation for monitoring

### Enhanced Logging

All errors now include structured context:

```php
Log::error('[WhoisHelper] WHOIS lookup failed for domain: '.$lead->domain.' with error: '.$errorMessage, [
    'error_type' => $errorType,
    'lead_id' => $lead->id,
    'domain' => $lead->domain,
    'exception_class' => get_class($e)
]);
```

This enables better monitoring and debugging through log aggregation tools.

## Database Migration

### Migration: `increase_domain_owner_column_length_in_leads_table`

**File**: `database/migrations/2025_05_30_084928_increase_domain_owner_column_length_in_leads_table.php`

**Changes**:
- Increases `domain_owner` column from `VARCHAR(50)` to `VARCHAR(255)`
- Maintains nullable and default value constraints
- Includes rollback capability (with data loss warning)

**Running the Migration**:
```bash
php artisan migrate
```

**Rollback** (if needed):
```bash
php artisan migrate:rollback --step=1
```

⚠️ **Warning**: Rolling back may cause data loss if any records have `domain_owner` longer than 50 characters.

## Testing

### Test Coverage

The test suite includes comprehensive coverage for error handling:

1. **Error Categorization Tests**: Verify correct classification of different exception types
2. **Domain Validation Tests**: Ensure domain validation logic remains intact
3. **Edge Case Tests**: Handle boundary conditions and special characters

### Running Tests

```bash
# Run all WhoisHelper tests
php artisan test tests/Unit/WhoisHelperTest.php

# Run with verbose output
php artisan test tests/Unit/WhoisHelperTest.php --verbose
```

### Test Structure

Error categorization tests use reflection to access private methods:

```php
it('categorizes database errors correctly', function () {
    $databaseError = new Exception('SQLSTATE[22001]: String data, right truncated...');
    $reflection = new ReflectionClass(WhoisHelper::class);
    $method = $reflection->getMethod('categorizeError');
    $method->setAccessible(true);
    
    expect($method->invoke(null, $databaseError))->toBe('database_error');
});
```

## Monitoring and Alerting

### Key Metrics to Monitor

1. **Error Rate by Type**: Track database vs network vs WHOIS errors
2. **Data Truncation Events**: Monitor frequency of domain_owner truncations
3. **Job Retry Patterns**: Identify domains with persistent network issues
4. **Column Length Usage**: Monitor if 255 characters is sufficient

### Log Queries

**Database Errors**:
```
[WhoisHelper] error_type:database_error
```

**Network Timeouts**:
```
[WhoisHelper] error_type:network_error "Connection timed out"
```

**Data Truncations**:
```
[WhoisHelper] "Truncated domain_owner"
```

## Performance Considerations

### Impact Assessment

1. **Migration Performance**: Column length change is fast for most databases
2. **Storage Impact**: Minimal increase in storage requirements
3. **Query Performance**: No impact on query performance
4. **Error Handling Overhead**: Negligible performance impact

### Optimization Opportunities

1. **Batch Processing**: Consider batching WHOIS lookups for better throughput
2. **Caching**: Implement WHOIS result caching for frequently queried domains
3. **Timeout Tuning**: Adjust socket timeout based on TLD-specific performance
4. **Retry Strategy**: Implement exponential backoff for network retries

## Future Improvements

### Potential Enhancements

1. **Smart Truncation**: Truncate at word boundaries rather than character limits
2. **TLD-Specific Timeouts**: Different timeout values for different TLDs
3. **Fallback WHOIS Servers**: Try alternative WHOIS servers on failure
4. **Rate Limiting**: Implement rate limiting to avoid being blocked by WHOIS servers

### Schema Considerations

1. **Separate Owner Table**: Consider normalizing domain owner data to separate table
2. **JSON Storage**: Store complete WHOIS response as JSON for future analysis
3. **Audit Trail**: Track WHOIS lookup history and changes over time

## Troubleshooting

### Common Issues

**Issue**: Migration fails with foreign key constraints
**Solution**: Temporarily disable foreign key checks during migration

**Issue**: Tests fail with reflection errors  
**Solution**: Ensure PHP reflection extension is enabled

**Issue**: Continued database errors after migration
**Solution**: Verify migration ran successfully and column length was updated

### Debug Commands

```bash
# Check current column definition
php artisan tinker
>>> Schema::getColumnType('leads', 'domain_owner')
>>> Schema::getColumnListing('leads')

# Test error categorization
>>> use App\Helpers\WhoisHelper;
>>> $reflection = new ReflectionClass(WhoisHelper::class);
>>> $method = $reflection->getMethod('categorizeError');
>>> $method->setAccessible(true);
>>> $method->invoke(null, new Exception('Connection timed out'));
```

## Conclusion

The improved error handling in `WhoisHelper` provides:

1. **Robust Error Recovery**: Graceful handling of database and network issues
2. **Better Monitoring**: Structured logging for improved observability  
3. **Reduced Data Loss**: Intelligent truncation preserves partial data
4. **Improved Reliability**: Appropriate retry strategies for different error types

These improvements ensure the WHOIS lookup process is more resilient and provides better visibility into issues when they occur.
