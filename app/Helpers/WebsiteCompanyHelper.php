<?php

namespace App\Helpers;

class WebsiteCompanyHelper
{
    /**
     * Prepares a context string from the HTML, preferring footer content, then body.
     * The returned string is lowercased and stripped of HTML tags.
     */
    public static function getContextTextForExtraction(string $htmlContent): string
    {
        if (preg_match('/<footer[^>]*>(.*?)<\\/footer>/is', $htmlContent, $footerMatches)) {
            // Check if footer content is substantial enough, otherwise, body might be better
            $footerText = strtolower(strip_tags($footerMatches[1]));
            if (strlen(trim($footerText)) > 50) { // Arbitrary threshold for "substantial"
                return $footerText;
            }
        }
        // Fallback to the entire body if footer is not found, empty, or not substantial
        if (preg_match('/<body[^>]*>(.*?)<\\/body>/is', $htmlContent, $bodyMatches)) {
            return strtolower(strip_tags($bodyMatches[1]));
        }

        // Ultimate fallback if body tag also fails (highly unlikely for valid HTML)
        return strtolower(strip_tags($htmlContent));
    }

    public static function extractTitle(string $htmlContent): ?string
    {
        if (preg_match('/<title[^>]*>(.*?)<\\/title>/is', $htmlContent, $matches)) {
            return trim(htmlspecialchars_decode($matches[1], ENT_QUOTES));
        }

        return null;
    }

    public static function extractMetaDescription(string $htmlContent): ?string
    {
        // More robust regex for meta description, handling various quote types and self-closing tags
        if (preg_match('/<meta\\s+(?:name\\s*=\\s*["\']description["\']\\s+content\\s*=\\s*["\'](.*?)["\']|content\\s*=\\s*["\'](.*?)["\']\\s+name\\s*=\\s*["\']description["\'])\\s*\\/?>/is', $htmlContent, $matches)) {
            // $matches[1] will be set if name comes first, $matches[2] if content comes first
            $description = ! empty($matches[1]) ? $matches[1] : (! empty($matches[2]) ? $matches[2] : null);
            if ($description !== null) {
                return trim(htmlspecialchars_decode($description, ENT_QUOTES));
            }
        }

        return null;
    }

    public static function extractHreflangs(string $htmlContent): ?string
    {
        if (preg_match_all('/<link[^>]*\\s+hreflang\\s*=\\s*["\']([^\"\']+)["\'][^>]*>/is', $htmlContent, $matches)) {
            if (! empty($matches[1])) {
                $uniqueHreflangs = array_unique(array_map('trim', $matches[1]));

                return implode(',', $uniqueHreflangs);
            }
        }

        return null;
    }

    public static function extractPhoneNumber(string $htmlContent): ?string
    {
        if (preg_match('/<a\\s[^>]*href\\s*=\\s*["\']tel:([^\"\']+)["\'][^>]*>/is', $htmlContent, $matches)) {
            $phoneNumberRaw = $matches[1]; // Already captured without 'tel:' due to regex group
            $phoneNumber = preg_replace('/[^0-9+]/', '', $phoneNumberRaw);

            return ! empty($phoneNumber) ? $phoneNumber : null;
        }

        return null;
    }

    public static function extractEmailAddress(string $htmlContent): ?string
    {
        if (preg_match('/<a\\s[^>]*href\\s*=\\s*["\']mailto:([^\"\']+)["\'][^>]*>/is', $htmlContent, $matches)) {
            $emailAddressRaw = $matches[1]; // Already captured without 'mailto:'
            // Remove query params like ?subject= and potential HTML entities in email
            $emailAddress = explode('?', $emailAddressRaw)[0];
            $emailAddress = html_entity_decode($emailAddress);

            return ! empty($emailAddress) ? trim($emailAddress) : null;
        }

        return null;
    }

    public static function extractPoweredBy(string $contextText): ?string
    {
        $poweredByPhrases = [
            'powered by', 'drevet af', 'angetrieben von', 'desarrollado por',
            'alimenté par', 'offerto da', 'drevet av', 'desenvolvido por',
            'alimentado por', 'drivs av',
        ];
        $patternParts = array_map(fn ($p) => preg_quote(trim($p), '~'), $poweredByPhrases);
        $regexPoweredBy = '~(?i)(?:'.implode('|', $patternParts).')\\s*([^|\\n\\r©<,.]{1,60})~';

        if (preg_match($regexPoweredBy, $contextText, $matches)) {
            if (isset($matches[1])) {
                $extractedName = trim($matches[1]);
                // Further clean up: remove " - CompanyName" if present
                if (str_contains($extractedName, ' - ')) {
                    $extractedName = trim(explode(' - ', $extractedName, 2)[0]);
                }
                // Remove trailing year like " WordPress 2024"
                $extractedName = preg_replace('/\\s+\\d{4}$/', '', $extractedName);
                $extractedName = trim($extractedName); // Trim again after year removal
                // Remove trailing period if it's the very last char: "WordPress."
                if (str_ends_with($extractedName, '.')) {
                    $extractedName = rtrim($extractedName, '.');
                }
                // Ensure meaningful extraction
                if (strlen($extractedName) > 1 && ! is_numeric($extractedName) && strtolower($extractedName) !== 'none') {
                    return $extractedName;
                }
            }
        }

        return null;
    }

    public static function extractVatNumber(string $contextText): ?string
    {
        // Regex for CVR/VAT: (cvr|vat|moms[nr]?) followed by 8 digits (with optional separators)
        $vatRegex = '/(?:cvr|vat|moms(?:nr)?)[.:\\s]*(\\d{2}[\\s.-]?\\d{2}[\\s.-]?\\d{2}[\\s.-]?\\d{2})/i';
        if (preg_match($vatRegex, $contextText, $matches)) {
            if (! empty($matches[1])) {
                // Normalize: remove spaces, dots, hyphens
                return str_replace([' ', '.', '-'], '', $matches[1]);
            }
        }

        return null;
    }

    public static function extractMadeBy(string $contextText, ?string $themeName = null): ?string
    {
        // First, try to extract from theme names if provided
        if ($themeName) {
            $extractedFromTheme = self::extractDeveloperFromThemeName($themeName);
            if ($extractedFromTheme) {
                return $extractedFromTheme;
            }
        }

        // Danish patterns for "made by" / "designed and developed by"
        $madeByPhrases = [
            'designet og udviklet af', 'udviklet af', 'designet af', 'lavet af',
            'skabt af', 'bygget af', 'made by', 'designed by', 'developed by',
            'created by', 'built by', 'angetrieben von',
            'desarrollado por', 'alimenté par', 'offerto da', 'drevet av',
            'desenvolvido por', 'alimentado por', 'drivs av',
        ];

        $regexMadeBy = '/(?:' . implode('|', array_map('preg_quote', $madeByPhrases)) . ')\\s+([^\\n\\r.]+?)(?:\\s+fra\\s+([^\\n\\r.]+?))?(?:[\\n\\r.]|$)/i';

        if (preg_match($regexMadeBy, $contextText, $matches)) {
            if (isset($matches[2]) && !empty(trim($matches[2]))) {
                // If we have "fra [company]" pattern, use the company name
                $extractedName = trim($matches[2]);
            } elseif (isset($matches[1])) {
                // Otherwise use the first captured group
                $extractedName = trim($matches[1]);
            } else {
                return null;
            }

            // Clean up the extracted name
            $extractedName = self::cleanExtractedName($extractedName);

            // Ensure meaningful extraction
            if (strlen($extractedName) > 1 && !is_numeric($extractedName) && strtolower($extractedName) !== 'none') {
                return $extractedName;
            }
        }

        return null;
    }

    /**
     * Extract developer/agency name from theme names
     */
    public static function extractDeveloperFromThemeName(string $themeName): ?string
    {
        // List of known Shopify developers/agencies
        $knownDevelopers = [
            'Kodence', // AT
            'ithelps', // AT
            'Studio Mitte Digital Media GmbH', // AT
            'Hanno Fäßler – dein eCommerce Wachstumspartner', // AT
            'ADTLANTIS', // AT
            'SIWA Online GmbH', // AT
            'MAILODY GmbH', // AT
            'MSTAGE GmbH', // AT
            'Lightport', // AT
            'ECOMBEAT', // AT
            '5pm.at | We’re only here to make you GO', // AT

            'Radikal', // BE
            'YourStoreMatters', // BE
            'Orssid Agency', // BE
            'wtb.agency', // BE
            'PureAgency', // BE
            'dear digital', // BE
            'Dynamate, powered by Esign', // BE
            'UTD', // BE
            'Kopstorm Comm.V', // BE
            'Meteor', // BE
            'Flip the bird ebvba', // BE
            'Webstaff', // BE
            'doo.coop', // BE
            'Wisefools', // BE
            'Agence Retis', // BE
            'SQLI', // BE
            'Juka.Retail', // BE
            'JACQ', // BE
            'Gube', // BE
            'YB Store Launch', // BE
            'Indie Group', // BE
            'Experience Lab', // BE

            'htmlBurger', // BG
            'Sherpas Design', // BG
            '✦ SEOexpert.bg | Бързи миграции от всички платформи, изработка на онлайн магазини, AI автоматизации, генеративна SEO оптимизация за ChatGPT, Google и други генеративни търсачки, редизайн на теми, SEO блог постове, оптимизирано съдържание и още', // BG
            'EPIX UX', // BG
            'Web Designer', // BG
            'Consentmo', // BG
            'Craftberry', // BG
            'Webrika', // BG

            'Delta K', // CY
            'Mynimal.io', // CY
            'Christian Drexler', // CY

            'Digismoothie Agency', // CZ
            'Sounds good agency s.r.o.', // CZ
            '3Node Software s.r.o.', // CZ
            'Appfleece', // CZ
            'Ecommerce Pot', // CZ
            'MageXo', // CZ
            'Data Lupa s.r.o.', // CZ

            '4TFM E-Commerce Agentur GmbH', // DE
            '3oneseven', // DE
            'Scale Solutions', // DE
            'Shopibrands', // DE
            'Slash Themes', // DE
            'apoio', // DE
            'Acceleratiq', // DE
            'Haketing e-commerce (GPSR Umsetzung)', // DE
            'Hyghstreet', // DE
            'Niccos Media GmbH', // DE
            'Finer Digital Agency', // DE
            'Greenblut GmbH', // DE
            'Falkeconsulting Berlin', // DE
            'Labrigart.Design GmbH', // DE
            'Eshop Guide', // DE
            'Tante-E GmbH', // DE
            'Latori GmbH', // DE
            'Kompetenz Kommerz', // DE
            'NEVER STOP Innovations', // DE
            'Karagoez Media Company', // DE
            'we-site GmbH', // DE
            'buero huegel', // DE
            'Helpingbrands', // DE
            'merconic GmbH', // DE
            'Createsome Community UG', // DE
            'Manthan Thummar', // DE
            'WEBWAVERS', // DE
            'Manuel Wirtz', // DE
            'HDC Digital GmbH', // DE
            'NETSHAKE', // DE
            'Shopfabrik Berlin GmbH', // DE
            'eBakery', // DE
            'commerce & code', // DE
            'Brand Boosting GmbH', // DE
            'eCommercely', // DE
            'Alpha Design', // DE
            'applepie', // DE
            'Moving Primates GmbH', // DE
            'J&J Ideenschmiede GmbH', // DE
            'shoplab', // DE
            'byteriver.de', // DE
            'CodeThat', // DE
            'bnymedia', // DE
            'BUZZWOO! GmbH & Co. KG', // DE
            'Rianthis', // DE
            'PsyCommerce', // DE
            'next level e-com | Nöthen, Arndt & Schult GbR', // DE
            'Ledist', // DE
            'ICHMIRMICH', // DE

            'Webshopskolen', // DK
            'Edition', // DK
            'D.TAILS', // DK
            'Digitalist', // DK
            'DVISIONMEDIA', // DK
            'ShopHelten', // DK
            'Signifly', // DK
            'Conversio', // DK
            'Eagle Media', // DK
            'MCB', // DK
            'Grafikr', // DK
            'Searchmind', // DK
            'Novicell', // DK
            'WDN', // DK
            '8Kilo', // DK
            'Clarify Commerce', // DK
            'Black Lemon', // DK
            'Looja', // DK
            'GROWBIX', // DK
            'Mercive', // DK
            'OkayScale', // DK
            'IEX', // DK
            'Anyday', // DK
            'Visibly', // DK
            'Fact', // DK
            'Webbler', // DK
            'The Luxurialist', // DK
            'KvDesign', // DK
            'Klartt', // DK
            'Amplify', // DK
            'Growify', // DK
            'Favikon', // DK
            'Kvalifik', // DK
            'Alpha Solutions', // DK
            'Lazy snail Design', // DK
            'Pixelz Services', // DK

            'Pandectes', // EE
            'Simplimo', // EE
            'Thorgate', // EE
            'Brandality', // EE
            'Holbi', // EE
            'Tutorsand', // EE
            'Northern Commerce', // EE
            'FinestShops', // EE
            'Arvi Design', // EE
            'Uvi Art', // EE

            'Fuznet', // FR
            'Le Petit Conseil', // FR
            'PLAY Digital', // FR
            'The Parisian', // FR
            'ShoppyLab', // FR
            'Shape & Shift', // FR
            'IT-Room', // FR
            'Monk Software', // FR
            'Axome', // FR
            'Alkeo', // FR

            'Netstudio', // GR
            'SimpleApps', // GR
            'Margento', // GR
            'FinestShops (Greece)', // GR
            'Global Touch', // GR

            'Shopiko', // HU
            'P3Media', // HU
            'WebShop Experts', // HU
            'Digiloop', // HU

            'Irish Shopify Experts', // IE
            'Magico', // IE
            'InnerCityDigital', // IE
            'Starbridge', // IE

            'Francesco', // IT
            'Soplify', // IT
            'Blodon', // IT
            'Nextre', // IT
            'Sintra Digital Business', // IT
            'Mediamente Consulting', // IT
            'The Commerce Guys (Italy)', // IT

            'MakesYouLocal', // LT
            'APG Media', // LT
            'BrainDock', // LT
            'TVSM', // LT

            'Magebit', // LV
            'Scandiweb', // LV
            'SOFTS', // LV

            'Ask Phill', // NL
            'Yellowgrape', // NL
            'Code', // NL
            'Friends of Commerce', // NL
            'Amsterdam Standard', // NL

            'Naturaily', // PL
            'Brand Active', // PL
            'Sallee Design', // PL
            'Sheer Code', // PL
            'Strix', // PL

            '27N', // PT
            'AUGE Agency', // PT
            'Fashioncan', // PT
            'Basicamente Digital', // PT
            'Commercelab', // PT
            'Skrey', // PT
            'Paulo Solinho', // PT
            'Alaska Agency', // PT
            'Bydas', // PT
            'Mintt Studio', // PT
            'Pedro Alfaiate', // PT
            'André Brito Coelho', // PT
            'Disaine', // PT
            'Evergreen Collective Co.', // PT
            'Elevate Digital Intelligence', // PT
            'Rebel.Online', // PT
            'Teo Digital Solutions', // PT

            'webmefy', // RO
            'We As Web', // RO
            'End Soft Design', // RO
            'ecomgeek', // RO

            'CartCoders', // SE
            'Made People', // SE
            'Woolman', // SE
            'Trellis', // SE

            'Biznification', // SK
            'Viking Software', // SK
            'Reachox', // SK

            'XeeDevelopers – eCommerce Websites Development Agency', // UK
            'Northwest Web Developments', // UK
            'Affinity Agency', // UK
            'Hi Ecom', // UK
            'Eastside Co®', // UK
            'Kubix', // UK
            'Heseven', // UK
            'Charle', // UK
            'Underwaterpistol', // UK
            'Swanky', // UK
            'BLU Commerce', // UK
            'We Make Websites', // UK
            'TVP Marketing', // UK
            'OMG Commerce', // UK
            'Kagool', // UK
        ];

        // Check if any known developer name appears in the theme name
        foreach ($knownDevelopers as $developer) {
            if (stripos($themeName, $developer) !== false) {
                return $developer;
            }
        }

        return null;
    }

    /**
     * Clean and normalize extracted developer/agency names
     */
    private static function cleanExtractedName(string $name): ?string
    {
        // Remove common suffixes and prefixes
        $name = preg_replace('/\s+(ApS|A\/S|AS|Ltd|Limited|Inc|Corp|Corporation|GmbH|AB|Oy|Aps)\.?$/i', '', $name);

        // Remove trailing year like "Company 2024"
        $name = preg_replace('/\s+\d{4}$/', '', $name);

        // Remove trailing period if it's the very last char
        $name = rtrim($name, '.');

        // Remove extra whitespace
        $name = preg_replace('/\s+/', ' ', trim($name));

        // Skip common non-developer terms
        $skipTerms = ['production', 'development', 'staging', 'test', 'demo', 'preview', 'live', 'main', 'primary'];
        if (in_array(strtolower($name), $skipTerms)) {
            return null;
        }

        // Ensure meaningful length
        if (strlen($name) < 2 || strlen($name) > 100) {
            return null;
        }

        return $name;
    }
}
