<?php

namespace App\Helpers;

use App\Models\Lead;
use Exception;
use Illuminate\Support\Facades\Log;
use Iodev\Whois\Factory as WhoisFactory;

class WhoisHelper
{
    /**
     * Socket timeout in seconds
     */
    const SOCKET_TIMEOUT = 30;

    /**
     * Validate domain format.
     *
     * @param  string  $domain  Domain to validate.
     * @return bool Whether domain is valid.
     */
    public static function isValidDomain(string $domain): bool
    {
        // Trim and convert to lowercase
        $domain = strtolower(trim($domain));

        // Check if domain is empty or too long
        if (empty($domain) || strlen($domain) > 255) {
            return false;
        }

        // Convert internationalized domain names (IDN) to ASCII (Punycode)
        // This handles domains like "unilån.dk" -> "xn--uniln-gra.dk"
        $asciiDomain = idn_to_ascii($domain, IDNA_DEFAULT, INTL_IDNA_VARIANT_UTS46);

        // If IDN conversion fails, try with the original domain
        if ($asciiDomain === false) {
            $asciiDomain = $domain;
        }

        // Enhanced domain validation regex
        // Checks for valid domain format with at least one dot and valid TLD
        // Supports ASCII domains and converted IDN domains
        $pattern = '/^(?:[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?\.)+[a-z0-9][a-z0-9-]{0,61}[a-z0-9]$/i';

        return (bool) preg_match($pattern, $asciiDomain);
    }

    /**
     * Fetch and update WHOIS information for a lead.
     *
     * @param  Lead  $lead  The lead model instance.
     *
     * @throws Exception When WHOIS operations fail or other critical errors occur.
     */
    public static function fetchAndUpdateWhoisInfo(Lead $lead): void
    {
        if (empty($lead->domain)) {
            Log::error('[WhoisHelper] Domain is empty for lead ID: '.$lead->id);

            return;
        }

        if (! self::isValidDomain($lead->domain)) {
            Log::error('[WhoisHelper] Invalid domain format for lead ID: '.$lead->id.' - Domain: '.$lead->domain);
            $lead->domain_status = 0; // Mark as unavailable or invalid
            $lead->save();

            return;
        }

        // Convert IDN domain to ASCII for WHOIS lookup
        $domainForWhois = idn_to_ascii($lead->domain, IDNA_DEFAULT, INTL_IDNA_VARIANT_UTS46);
        if ($domainForWhois === false) {
            $domainForWhois = $lead->domain; // Fallback to original if conversion fails
        }

        Log::info('[WhoisHelper] Performing WHOIS lookup for domain: '.$lead->domain.' (ASCII: '.$domainForWhois.') (Lead ID: '.$lead->id.')');

        $originalTimeout = ini_get('default_socket_timeout');
        try {
            ini_set('default_socket_timeout', self::SOCKET_TIMEOUT);

            $whois = WhoisFactory::get()->createWhois();
            $info = $whois->loadDomainInfo($domainForWhois);

            if ($info) {
                $lead->domain_status = 1; // Domain is available/active
                $lead->domain_created = $info->creationDate ? date('Y-m-d H:i:s', $info->creationDate) : null;
                $lead->domain_expires = $info->expirationDate ? date('Y-m-d H:i:s', $info->expirationDate) : null;
                $lead->domain_owner = $info->owner ?? 'Unknown Owner';
                $lead->save();
                Log::info('[WhoisHelper] Successfully updated WHOIS information for domain: '.$lead->domain);
            } else {
                $lead->domain_status = 0; // Domain not found or not available
                $lead->save();
                Log::warning('[WhoisHelper] Domain not found in WHOIS for lead ID: '.$lead->id.' - Domain: '.$lead->domain);
            }
        } catch (Exception $e) {
            Log::error('[WhoisHelper] WHOIS lookup failed for domain: '.$lead->domain.' with error: '.$e->getMessage());
            $lead->domain_status = 0; // Mark as unavailable due to error
            $lead->save();
            throw $e; // Re-throw to be caught by the job's error handling for retries
        } finally {
            ini_set('default_socket_timeout', $originalTimeout);
        }
    }
}
